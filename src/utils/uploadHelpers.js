import { calculateMD5 } from './md5';

/**
 * Split a file into chunks for multipart upload
 * @param {File} file - The file to split
 * @param {number} chunkSize - Size of each chunk in bytes (default: 5MB)
 * @returns {Array<Blob>} Array of file chunks
 */
export const splitFileIntoChunks = (file, chunkSize = 5 * 1024 * 1024) => {
  const chunks = [];
  let start = 0;

  while (start < file.size) {
    const end = Math.min(start + chunkSize, file.size);
    const chunk = file.slice(start, end);
    chunks.push(chunk);
    start = end;
  }

  return chunks;
};

/**
 * Upload a single chunk to a pre-signed URL
 * @param {string} url - Pre-signed URL for upload
 * @param {Blob} chunk - File chunk to upload
 * @param {string} contentType - Content type of the file
 * @param {string} contentMd5 - Base64 encoded MD5 hash of the chunk
 * @returns {Promise<Object>} Response with ETag
 */
export const uploadChunk = async (url, chunk, contentType, contentMd5) => {
  const response = await fetch(url, {
    method: 'PUT',
    body: chunk,
    headers: {
      'Content-Type': contentType,
      'Content-MD5': contentMd5,
      'Content-Length': chunk.size.toString(),
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to upload chunk: ${response.status} ${response.statusText}`);
  }

  // Extract ETag from response headers
  const etag = response.headers.get('ETag');
  return { etag: etag ? etag.replace(/"/g, '') : '' };
};

/**
 * Upload a file using multipart upload with progress tracking
 * @param {Object} options - Upload options
 * @param {File} options.file - File to upload
 * @param {Function} options.initiateUpload - Function to initiate multipart upload
 * @param {Function} options.getPartUrl - Function to get upload URL for a part
 * @param {Function} options.completeUpload - Function to complete multipart upload
 * @param {Function} options.onProgress - Progress callback function
 * @param {number} options.chunkSize - Size of each chunk in bytes (default: 5MB)
 * @returns {Promise<Object>} Upload result
 */
export const multipartUpload = async ({
  file,
  initiateUpload,
  getPartUrl,
  completeUpload,
  onProgress = () => { },
  chunkSize = 5 * 1024 * 1024
}) => {
  try {
    // Step 1: Initiate multipart upload
    const initiateResponse = await initiateUpload({
      filename: file.name,
      content_type: file.type,
      content_size: file.size.toString(),
      resource_type: "Public"
    });

    const { object_key, resource } = initiateResponse.data;
    // Validate required response data
    if (!object_key || typeof object_key !== 'string') {
      throw new Error('Invalid object_key received from multipart upload initiation');
    }
    if (!resource || !resource.id) {
      throw new Error('Invalid resource data received from multipart upload initiation');
    }

    // Step 2: Split file into chunks
    const chunks = splitFileIntoChunks(file, chunkSize);
    const totalChunks = chunks.length;
    const parts = [];

    // Step 3: Upload each chunk
    for (let i = 0; i < totalChunks; i++) {
      const partNumber = i + 1;
      const chunk = chunks[i];

      try {
        // Calculate MD5 for this chunk with enhanced security options
        const chunkMd5 = await calculateMD5(chunk, {
          maxSize: chunkSize * 2, // Allow up to 2x chunk size for safety
          retries: 2, // Fewer retries for chunks to avoid long delays
        });

        // Validate chunk MD5 format
        if (!chunkMd5 || typeof chunkMd5 !== 'string' || chunkMd5.length === 0) {
          throw new Error(`Invalid MD5 hash calculated for chunk ${partNumber}`);
        }

        // Get pre-signed URL for this part
        const partUrlParams = {
          resource_id: resource.id,
          content_md5: chunkMd5,
          part_number: partNumber,
          content_length: chunk.size.toString(),
          object_key: object_key,
        };

        // Validate all required parameters are present
        const requiredParams = ['resource_id', 'content_md5', 'part_number', 'content_length', 'object_key'];
        for (const param of requiredParams) {
          if (!partUrlParams[param] || (typeof partUrlParams[param] !== 'string' && typeof partUrlParams[param] !== 'number')) {
            throw new Error(`Missing or invalid required parameter '${param}' for /upload/part/url API call. Value: ${partUrlParams[param]}`);
          }
        }

        const urlResponse = await getPartUrl(partUrlParams);

        const uploadUrl = urlResponse.data.url;

        // Upload the chunk with retry logic
        let uploadAttempts = 0;
        const maxUploadAttempts = 3;
        let etag;

        while (uploadAttempts < maxUploadAttempts) {
          try {
            const result = await uploadChunk(uploadUrl, chunk, file.type, chunkMd5);
            etag = result.etag;
            break;
          } catch (uploadError) {
            uploadAttempts++;
            console.warn(`Chunk ${partNumber} upload attempt ${uploadAttempts} failed:`, uploadError.message);

            if (uploadAttempts >= maxUploadAttempts) {
              throw new Error(`Failed to upload chunk ${partNumber} after ${maxUploadAttempts} attempts: ${uploadError.message}`);
            }

            // Wait before retry (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, uploadAttempts) * 1000));
          }
        }

        // Validate ETag
        if (!etag || typeof etag !== 'string') {
          console.warn(`Warning: Invalid ETag received for chunk ${partNumber}: ${etag}`);
        }

        parts.push({
          part_number: partNumber,
          etag,
        });

        // Report progress
        onProgress((i + 1) / totalChunks * 100);

      } catch (chunkError) {
        console.error(`Failed to process chunk ${partNumber}:`, chunkError);
        throw new Error(`Chunk ${partNumber} processing failed: ${chunkError.message}`);
      }
    }

    // Step 4: Complete multipart upload
    await completeUpload({
      resource_id: resource.id,
    });

    return resource;
  } catch (error) {
    console.error('Multipart upload failed:', error);
    throw error;
  }
};

/**
 * Format file size in human-readable format
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Get file extension from filename
 * @param {string} filename - File name
 * @returns {string} File extension
 */
export const getFileExtension = (filename) => {
  return filename.split('.').pop().toLowerCase();
};

/**
 * Check if file is an image
 * @param {File} file - File to check
 * @returns {boolean} True if file is an image
 */
export const isImageFile = (file) => {
  const imageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
  return imageTypes.includes(file.type);
};

/**
 * Check if file is a video
 * @param {File} file - File to check
 * @returns {boolean} True if file is a video
 */
export const isVideoFile = (file) => {
  const videoTypes = [
    'video/mp4',
    'video/webm',
    'video/ogg',
    'video/quicktime', // .mov
    'video/x-msvideo', // .avi
    'video/x-matroska' // .mkv
  ];
  return videoTypes.includes(file.type);
};

/**
 * Detect file type category
 * @param {File} file - File to categorize
 * @returns {string} File category ('image', 'video', 'other')
 */
export const detectFileType = (file) => {
  if (isImageFile(file)) return 'image';
  if (isVideoFile(file)) return 'video';
  return 'other';
};

/**
 * Determine if file should use multipart upload based on size
 * @param {File} file - File to check
 * @param {number} threshold - Size threshold in bytes (default: 5MB)
 * @returns {boolean} True if should use multipart upload
 */
export const shouldUseMultipartUpload = (file, threshold = 5 * 1024 * 1024) => {
  // Use multipart upload for any file larger than the threshold
  return file.size > threshold;
};

/**
 * Convert file to base64 string
 * @param {File} file - File to convert
 * @returns {Promise<string>} Base64 string representation of the file
 */
export const fileToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
};



/**
 * Separate files by type for multiple image / single video handling
 * @param {Array<File>} files - Array of files to separate
 * @returns {Object} Object with images and videos arrays
 */
export const separateFilesByType = (files) => {
  const images = [];
  const videos = [];

  files.forEach(file => {
    if (isImageFile(file)) {
      images.push(file);
    } else if (isVideoFile(file)) {
      videos.push(file);
    }
  });

  return { images, videos };
};

/**
 * Generic AWS upload utility for any file type using pre-signed URLs
 * @param {Object} options - Upload options
 * @param {File} options.file - File to upload
 * @param {Function} options.getUploadUrl - Function to get pre-signed URL
 * @param {Function} options.onProgress - Progress callback function
 * @param {number} options.progressWeight - Weight of this upload in overall progress (default: 100)
 * @returns {Promise<Object>} Upload result with URL and object key
 */
export const uploadToAWS = async ({
  file,
  getUploadUrl,
  onProgress = () => { },
  progressWeight = 100,
}) => {
  try {
    // Step 1: Calculate MD5 hash (10% of progress weight)
    const md5Progress = progressWeight * 0.1;
    onProgress(md5Progress * 0.5); // Start MD5 calculation
    const contentMd5 = await calculateMD5(file);
    onProgress(md5Progress); // MD5 calculation complete

    // Step 2: Get upload URL (20% of progress weight)
    const urlProgress = progressWeight * 0.2;
    onProgress(md5Progress + urlProgress * 0.5); // Start getting URL
    const uploadResponse = await getUploadUrl({
      filename: file.name,
      content_type: file.type,
      content_md5: contentMd5,
      content_size: file.size.toString(),
    });
    onProgress(md5Progress + urlProgress); // URL retrieval complete

    const { url, method, object_key, resource_url } = uploadResponse.data;

    // Step 3: Upload file to AWS (70% of progress weight)
    const uploadProgressStart = md5Progress + urlProgress;
    const uploadProgressWeight = progressWeight * 0.7;

    onProgress(uploadProgressStart + uploadProgressWeight * 0.1); // Start upload

    const awsResponse = await fetch(url, {
      method: method || 'PUT',
      body: file,
      headers: {
        'Content-Type': file.type,
        'Content-MD5': contentMd5,
        'Content-Length': file.size.toString(),
      },
    });

    if (!awsResponse.ok) {
      throw new Error(`AWS upload failed: ${awsResponse.status} - ${awsResponse.statusText}`);
    }

    onProgress(progressWeight); // Upload complete

    return {
      url: resource_url || url.split('?')[0], // Use resource_url if available, otherwise clean URL
      object_key,
      uploadResponse: uploadResponse.data,
    };
  } catch (error) {
    console.error('AWS upload failed:', error);
    throw error;
  }
};

/**
 * Upload a file using direct upload (for images)
 * @param {Object} options - Upload options
 * @param {File} options.file - File to upload
 * @param {Function} options.getUploadUrl - Function to get upload URL
 * @param {Function} options.completeUpload - Function to complete upload
 * @param {Function} options.onProgress - Progress callback function
 * @returns {Promise<Object>} Upload result with resource details
 */
export const directUpload = async ({
  file,
  getUploadUrl,
  completeUpload,
  onProgress = () => { },
}) => {
  try {
    // Use the new uploadToAWS utility for the upload part (90% of progress)
    const awsResult = await uploadToAWS({
      file,
      getUploadUrl: (data) => getUploadUrl({ ...data, resource_type: "Public" }),
      onProgress: (progress) => onProgress(progress * 0.9), // 90% for upload
      progressWeight: 90,
    });

    onProgress(95); // 95% - upload complete, completing...

    // Step 4: Complete the upload by calling the completion endpoint
    if (completeUpload && awsResult.uploadResponse.resource?.id) {
      try {
        await completeUpload({
          resource_id: awsResult.uploadResponse.resource.id,
        });
      } catch (completionError) {
        console.error('Upload completion failed:', completionError);
        throw new Error(`Upload completion failed: ${completionError.message}`);
      }
    }

    onProgress(100); // 100% - upload and completion done
    return awsResult.uploadResponse.resource;

  } catch (error) {
    console.error('Direct upload failed:', error);
    throw error;
  }
};